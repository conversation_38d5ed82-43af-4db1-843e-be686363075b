{"name": "gg-catalog-web", "version": "0.1.0", "private": true, "scripts": {"dev": "concurrently \"next dev --turbopack\" \"nodemon API/index.js\"", "dev:next": "next dev --turbopack", "dev:api": "nodemon API/index.js", "setup:db": "node API/APISetup.js", "test:api": "node API/test.js", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@types/qrcode": "^1.5.5", "axios": "^1.11.0", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-session": "^1.18.2", "express-validator": "^7.2.1", "helmet": "^8.1.0", "multer": "^2.0.2", "mysql2": "^3.14.2", "next": "15.4.4", "qrcode": "^1.5.4", "react": "19.1.0", "react-dom": "19.1.0", "react-image-crop": "^11.0.10", "sharp": "^0.34.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.2.0", "eslint": "^9", "eslint-config-next": "15.4.4", "nodemon": "^3.1.10", "tailwindcss": "^4", "typescript": "^5"}}